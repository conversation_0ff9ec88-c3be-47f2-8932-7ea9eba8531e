package org.eu.ump90.tradej.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.eu.ump90.tradej.bean.domain.Kline1d;

import java.util.List;

@Mapper
public interface Kline1dMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Kline1d record);

    int insertSelective(Kline1d record);

    Kline1d selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Kline1d record);

    int updateByPrimaryKey(Kline1d record);

    int batchInsert(List<Kline1d> records);

    List<Kline1d> findBySymbol(String symbol);
}