package org.eu.ump90.tradej.api;

import com.dtflys.forest.annotation.GetRequest;
import com.dtflys.forest.annotation.Var;
import java.io.InputStream;

public interface BinanceAPI {

  /**
   * 返回类型用InputStream，用流的方式读取文件内容
   * https://data.binance.vision/data/spot/monthly/klines/BTCUSDT/1h/BTCUSDT-1h-2025-06.zip
   */
  @GetRequest(
      url =
          "https://data.binance.vision/data/spot/monthly/klines/{symbol}/{interval}/{symbol}-{interval}-{year}-{month}.zip")
  InputStream downloadZipFile(
      @Var("symbol") String symbol,
      @Var("interval") String interval,
      @Var("year") String year,
      @Var("month") String month);
}
