package org.eu.ump90.tradej.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eu.ump90.tradej.api.BinanceAPI;
import org.eu.ump90.tradej.service.SyncDataService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "sync")
@RequiredArgsConstructor
@Slf4j
public class SyncDataController {

  private final SyncDataService syncDataService;

  private final BinanceAPI binanceApi;

  @GetMapping
  public void syncDate(
      @RequestParam String symbol,
      @RequestParam String interval,
      @RequestParam String startTime,
      @RequestParam String endTime) {


      binanceApi.downloadZipFile(symbol,interval,)




  }
}
