package org.eu.ump90.tradej.bean.enums;

// All kline intervals are supported:
//
// 1s, 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1mo.
// 1mo is used instead of 1M to supprt non-case sensitive file systems.
public enum IntervalEnum {
    
    ONE_SECOND("1s"),
    ONE_MINUTE("1m"),
    THREE_MINUTES("3m"),
    FIVE_MINUTES("5m"),
    FIFTEEN_MINUTES("15m"),
    THIRTY_MINUTES("30m"),
    ONE_HOUR("1h"),
    TWO_HOURS("2h"),
    FOUR_HOURS("4h"),
    SIX_HOURS("6h"),
    EIGHT_HOURS("8h"),
    TWELVE_HOURS("12h"),
    ONE_DAY("1d"),
    THREE_DAYS("3d"),
    ONE_WEEK("1w"),
    ONE_MONTH("1mo");
    
    private final String value;
    
    IntervalEnum(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    public static IntervalEnum fromValue(String value) {
        for (IntervalEnum interval : values()) {
            if (interval.value.equals(value)) {
                return interval;
            }
        }
        throw new IllegalArgumentException("Unknown interval: " + value);
    }
    
    @Override
    public String toString() {
        return value;
    }
}
